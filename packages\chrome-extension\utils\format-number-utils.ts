export const getNums = (num: number | string) => {
  return Number.parseInt(Number(num).toFixed(3))
}
export const getFloat = (num?: number | string, position = 2) => {
  const res = Number.parseFloat(String(Number(num).toFixed(position)))
  return isNaN(res) ? (num as number) : (res as unknown as number)
}
export const getInt = (num?: number | string) => {
  const res = Number.parseInt(String(Number(num).toFixed(0)))
  return isNaN(res) ? (num as number) : (res as unknown as number)
}
/**
 * 从带单位的数值字符串中提取纯数值部分（浮点数）
 * @param num 可能带单位的数值，如 "15.5px", "15.8rem", "15dp" 等
 * @param position 保留小数位数，默认为2
 * @returns 提取出的浮点数值
 * @example
 * getNumericFloat("15.5px") // 15.5
 * getNumericFloat("15.8rem", 1) // 15.8
 * getNumericFloat("15") // 15
 * getNumericFloat(15.5) // 15.5
 */
export const getNumericFloat = (num?: number | string, position = 2) => {
  if (num === undefined || num === null) {
    return 0
  }

  // 如果是数字类型，直接使用 getFloat 处理
  if (typeof num === 'number') {
    return getFloat(num, position)
  }

  // 如果是字符串，先提取数值部分
  const numericPart = String(num).match(/^-?\d*\.?\d+/)
  if (numericPart) {
    return getFloat(numericPart[0], position)
  }

  // 如果无法提取数值，返回原值（保持与 getFloat 一致的行为）
  return getFloat(num, position)
}

export function fixNum(number: number, fractionDigits = 2) {
  if (number % 1 !== 0) {
    return number.toFixed(fractionDigits)
  } else {
    return Number(number).toString()
  }
}
