const metaKey = Reflect.getOwnPropertyDescriptor(MouseEvent.prototype, 'metaKey')!
const ctrlKey = Reflect.getOwnPropertyDescriptor(MouseEvent.prototype, 'ctrlKey')!
const altKey = Reflect.getOwnPropertyDescriptor(MouseEvent.prototype, 'altKey')!

// 检测操作系统 - 使用 userAgent 替代已弃用的 platform
const isMacOS = /Mac|iPhone|iPod|iPad/i.test(navigator.userAgent)

export function setDeepSelectionKey(lock: boolean) {
  if (isMacOS) {
    // macOS 使用 metaKey (Cmd 键)
    if (lock) {
      Reflect.defineProperty(MouseEvent.prototype, 'metaKey', {
        get: () => true,
      })
    } else {
      Reflect.defineProperty(MouseEvent.prototype, 'metaKey', metaKey)
    }
  } else {
    // Windows/Linux 使用 ctrlKey (Ctrl 键)
    if (lock) {
      Reflect.defineProperty(MouseEvent.prototype, 'ctrlKey', {
        get: () => true,
      })
    } else {
      Reflect.defineProperty(MouseEvent.prototype, 'ctrlKey', ctrlKey)
    }
  }
}

export function setAltKey(lock: boolean) {
  if (lock) {
    Reflect.defineProperty(MouseEvent.prototype, 'altKey', {
      get: () => true,
    })
  } else {
    Reflect.defineProperty(MouseEvent.prototype, 'altKey', altKey)
  }
}
