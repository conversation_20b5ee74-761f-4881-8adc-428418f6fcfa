import Tooltip from '@/components/ui/toolTip'
interface IExportComProps {
  highlightedCode: string
  copyResult?: string
}
const CodeCom = (props: IExportComProps) => {
  const {highlightedCode, copyResult} = props
  const [tooltipInfo, setToolTipInfo] = useState<{open: boolean; title: string}>({open: false, title: ''})
  const [coping, setCoping] = useState<boolean>(false)
  const copy = useCallback(() => {
    if (coping) {
      return
    }
    copyToClipboard(copyResult || highlightedCode).then((res: string) => {
      setToolTipInfo({open: true, title: res})
      const id = window.setTimeout(() => {
        clearTimeout(id)
        setToolTipInfo({open: false, title: ''})
        setCoping(false)
      }, 1200)
    })
  }, [copyResult])
  return (
    <Tooltip open={tooltipInfo.open} content={tooltipInfo.title}>
      <div className="w-full h-fit relative">
        <div
          onClick={() => {
            copy()
          }}
          className={`absolute z-10 right-2 top-2 w-6 h-6 cursor-pointer bg-[url('https://hd-static.yystatic.com/5043395892242635.png')] active:bg-[url('https://hd-static.yystatic.com/4091166436919704.png')]`}
        ></div>
        <pre className="bg-[#F7F8FA] mb-[10px] pl-2.5 pr-2.5 pt-2 pb-2 rounded-[8px] text-xs relative w-full max-h-[488px] scrollbarx overflow-x-auto">
          <code
            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
            dangerouslySetInnerHTML={{
              __html: highlightedCode,
            }}
          ></code>
        </pre>
      </div>
    </Tooltip>
  )
}
export default CodeCom
