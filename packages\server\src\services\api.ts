import {logger} from './logger'

// 定义请求格式类型
export type ImageFormat = 'jpg' | 'png' | 'svg' | 'pdf'
export type FigmaHeaderToken = {
  accessToken: string
  personalToken: string
}
export class FigmaAPI {
  private static instance: FigmaAPI
  private baseURL = 'https://api.figma.com/v1'
  public headers: Record<string, string> & {
    'X-Figma-Token': string
    Authorization?: string
  } = {
    'X-Figma-Token': '',
  }

  // 修改为私有构造函数
  private constructor() {}

  // 单例模式获取实例
  static getInstance(): FigmaAPI {
    if (!FigmaAPI.instance) {
      FigmaAPI.instance = new FigmaAPI()
    }
    return FigmaAPI.instance
  }

  setToken(token: FigmaHeaderToken) {
    if (token.accessToken) {
      this.headers = {
        Authorization: `Bearer ${token.accessToken}`,
        'X-Figma-Token': token.accessToken,
      }
    } else if (token.personalToken) {
      this.headers = {
        'X-Figma-Token': token.personalToken,
      }
    }
    return this
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T | {error: boolean; message: string; status?: number; statusText?: string}> {
    const url = `${this.baseURL}${endpoint}`
    const requestOptions = {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
      // 设置30秒超时
      signal: AbortSignal.timeout(90000),
    }

    const startTime = Date.now()
    logger.logApiRequest(options.method || 'GET', url, null, requestOptions.headers)

    try {
      const response = await fetch(url, requestOptions)
      const responseTime = Date.now() - startTime

      if (!response.ok) {
        const errorMessage = `API请求失败: ${response.status} ${response.statusText}`
        logger.error(errorMessage)
        logger.logApiResponse(options.method || 'GET', url, response.status, responseTime, {error: true})

        return {
          error: true,
          message: errorMessage,
          status: response.status,
          statusText: response.statusText,
        }
      }

      const data = await response.json()
      logger.logApiResponse(options.method || 'GET', url, response.status, responseTime)
      // logger.debug(`API响应数据: ${JSON.stringify(data)}`)

      return data as T
    } catch (error) {
      const responseTime = Date.now() - startTime
      let errorMessage = `请求失败: ${url}`

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.name === 'AbortError' || error.name === 'TimeoutError') {
          errorMessage += `, 请求超时(30秒): ${error.message}`
        } else {
          errorMessage += `, ${error.message}`
        }
      } else {
        errorMessage += `, ${String(error)}`
      }

      logger.error(errorMessage, error)
      logger.logApiResponse(options.method || 'GET', url, 0, responseTime, {error: true, message: errorMessage})

      return {
        error: true,
        message: errorMessage,
      }
    }
  }

  // 保留实际使用的API方法
  async getFile(fileKey: string) {
    logger.info(`获取 Figma 文件: ${fileKey}`)
    return this.request(`/files/${fileKey}`)
  }

  // async getFileNodes(fileKey: string, nodeIds: string[], urlParams?: any): Promise<any> {
  //   let p
  //   {
  //     const {nodeIds, fileKey, ...params} = urlParams
  //     p = params
  //   }
  //   const ids = nodeIds.join(',')
  //   logger.info(`获取 Figma 节点: fileKey=${fileKey}, nodeIds=${ids}`)
  //   return this.request(`/files/${fileKey}/nodes?ids=${ids}`)
  // }

  async getFileNodes(fileKey: string, nodeIds: string[], urlParams?: any): Promise<any> {
    // 解构出其他参数
    const {
      nodeIds: _nodeIds,
      fileKey: _fileKey,
      personal_token: _personal_token,
      access_token: _access_token,
      format: _format,
      ...params
    } = urlParams || {}

    // 将节点ID转换为逗号分隔的字符串
    const ids = nodeIds.join(',')

    // 构建查询字符串
    const queryParams = new URLSearchParams({
      ids,
      ...params,
    }).toString()

    logger.info(`获取 Figma 节点: fileKey=${fileKey}, nodeIds=${ids}`)
    return this.request(`/files/${fileKey}/nodes?${queryParams}`)
  }

  // async getImage(
  //   fileKey: string,
  //   nodeIds: string[],
  //   params: {format: ImageFormat; scale: number} = {format: 'png', scale: 1},
  // ): Promise<any> {
  //   // logger.info(`获取 Figma 图像 params: ${JSON.stringify(params)}`)
  //   const ids = nodeIds.join(',')
  //   logger.info(`获取 Figma 图像: fileKey=${fileKey}, nodeIds=${ids}, format=${params.format}`)
  //   return this.request(`/images/${fileKey}?ids=${ids}&format=${params.format ?? 'png'}&scale=${params.scale ?? '1'}`)
  // }
  async getImage(
    fileKey: string,
    nodeIds: string[],
    params: {format: ImageFormat; scale: number} = {format: 'png', scale: 1},
  ): Promise<any> {
    const batchSize = 150
    const numBatches = Math.ceil(nodeIds.length / batchSize)
    const results: any[] = []

    logger.info(`获取 Figma 图像: fileKey=${fileKey}, nodeIds.length=${nodeIds.length}, format=${params.format}`)

    // 分批处理
    const batches = Array.from({length: numBatches}, (_, i) => {
      const start = i * batchSize
      const end = start + batchSize
      const batchIds = nodeIds.slice(start, end)
      const ids = batchIds.join(',')

      logger.info(`获取 Figma 图像 (batch ${i + 1}/${numBatches}): nodeIds=${ids}`)

      return this.request<any>(
        `/images/${fileKey}?ids=${ids}&format=${params.format ?? 'png'}&scale=${params.scale ?? '1'}`,
      )
    })

    // 并发请求
    try {
      const responses = await Promise.all(batches)

      // 合并结果
      responses.forEach(response => {
        if (response && response.images) {
          Object.assign(results, response.images)
        }
      })

      return {images: results}
    } catch (error) {
      logger.error(`获取 Figma 图像失败: ${error}`)
      return {error: true, message: String(error)}
    }
  }

  async getProjectFiles(projectId: string) {
    logger.info(`获取项目文件: projectId=${projectId}`)
    return this.request(`/projects/${projectId}/files`)
  }

  async getMe(): Promise<any> {
    // logger.info(`获取 Figma 图像 params: ${JSON.stringify(params)}`)
    logger.info(`获取 个人信息`)
    return this.request(`/me`)
  }
}

// 将默认导出改为命名导出
export const figmaAPI = FigmaAPI.getInstance()
