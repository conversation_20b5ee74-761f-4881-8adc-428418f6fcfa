import {useAppLogic} from '@/components/AppProvider'
import {Modal} from '@/components/ui/dialog/Modal'
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover'
import dimensionStore from '@/store/dimensionStore'

const Header = () => {
  const {currentNodeId, curUrl, getCurUrl} = useAppLogic()

  return (
    <>
      <header className="flex items-center mb-2 px-3 pt-4">
        <div className={`text-sm flex-auto`}>
          当前选中节点：
          {currentNodeId ? (
            <Popover onOpenChange={open => open && getCurUrl()}>
              <PopoverTrigger>
                <span className={`font-bold cursor-pointer`}>{currentNodeId ? currentNodeId : '暂无'}</span>
              </PopoverTrigger>
              <PopoverContent className="break-words overflow-y-auto max-h-[400px]">{curUrl}</PopoverContent>
            </Popover>
          ) : (
            <span className={`font-bold`}>{currentNodeId ? currentNodeId : '暂无'}</span>
          )}
        </div>
      </header>
      <Modal
        open={!!dimensionStore.state.showSettingsModal}
        onOpenChange={open => !open && dimensionStore.setModalShow(null)}
        {...dimensionStore.state.showSettingsModal}
      ></Modal>
    </>
  )
}

export default Header
